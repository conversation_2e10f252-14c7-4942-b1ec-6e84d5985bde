import 'dart:convert';
import 'package:http/http.dart' as http;

class DeepSeekApiService {
  static const String _baseUrl = 'https://api.deepseek.com/v1';
  static const String _apiKey = 'sk-058aa01396e94c498f878515ce782771';

  static const Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $_apiKey',
  };

  /// Send a message to DeepSeek API and get a response
  static Future<String> sendMessage({
    required List<Map<String, String>> messages,
    String model = 'deepseek-chat',
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: _headers,
        body: json.encode({
          'model': model,
          'messages': messages,
          'temperature': temperature,
          'max_tokens': maxTokens,
          'stream': false,
          'top_p': 0.95, // Nucleus sampling to reduce artifacts
          'frequency_penalty': 0.1, // Reduce repetitive tokens
          'presence_penalty': 0.1, // Encourage diverse vocabulary
          'stop': ['\$', '1', '<|endoftext|>'], // Stop on common artifacts
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        String content =
            data['choices'][0]['message']['content'] ?? 'No response received.';

        // Additional cleaning at API level
        content = _cleanTokenizationArtifacts(content);

        return content;
      } else {
        throw Exception('API Error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  /// Clean tokenization artifacts at the API service level
  static String _cleanTokenizationArtifacts(String content) {
    String cleaned = content;

    // Remove common DeepSeek tokenization artifacts
    cleaned = cleaned.replaceAll(RegExp(r'\$+'), ''); // Dollar signs
    cleaned = cleaned.replaceAll(RegExp(r'\b1+\b'), ''); // Standalone 1's
    cleaned = cleaned.replaceAll(RegExp(r'▁+'), ' '); // Unicode space tokens
    cleaned = cleaned.replaceAll(RegExp(r'<\|.*?\|>'), ''); // Special tokens

    // Clean up whitespace
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    cleaned = cleaned.trim();

    return cleaned;
  }

  /// Create a system message for counselor personality
  static Map<String, String> createSystemMessage(String content) {
    return {'role': 'system', 'content': content};
  }

  /// Create a user message
  static Map<String, String> createUserMessage(String content) {
    return {'role': 'user', 'content': content};
  }

  /// Create an assistant message
  static Map<String, String> createAssistantMessage(String content) {
    return {'role': 'assistant', 'content': content};
  }
}
